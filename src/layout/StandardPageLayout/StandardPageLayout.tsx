import React, { ReactNode, useEffect } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import { sharedState } from "@src/shared-state/shared-state";
import SeaFooter from "@src/components/_legacy/SeaFooter/SeaFooter";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { colors } from "@src/theme/colors";

interface StandardPageLayoutProps {
  children: ReactNode;
  scrollEvents?: boolean;
  onScroll?: (event: any) => void;
}

const StandardPageLayout: React.FC<StandardPageLayoutProps> = ({
  children,
  scrollEvents = false,
  onScroll,
}) => {
  useEffect(() => {
    sharedState.layoutMode.set("standard");
  }, []);

  // Determine layout based on screen width
  const { isDesktopWidth, isTabletWidth, isMobileWidth } = useDeviceWidth();

  return (
    <View
      style={[
        styles.container,
        isDesktopWidth && {
          paddingTop: 12,
          paddingLeft: 12,
        },
      ]}
    >
      <View
        style={[
          styles.wrapper,
          isDesktopWidth && {
            borderTopLeftRadius: 36,
          },
        ]}
      >
        {/*<ScrollView*/}
        {/*  onScroll={onScroll}*/}
        {/*  style={styles.scrollArea}*/}
        {/*  contentContainerStyle={styles.contentContainer}*/}
        {/*>*/}
        <View
          style={[
            styles.content,
            isMobileWidth && styles.mobileContent,
            isTabletWidth && styles.tabletContent,
            isDesktopWidth && styles.desktopContent,
          ]}
        >
          {children}
        </View>
        {/*</ScrollView>*/}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  wrapper: {
    flex: 1,
    backgroundColor: colors.background.primary,
    borderColor: colors.borderColor,
    borderWidth: 2,
  },
  scrollArea: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: "100%",
  },
  content: {
    flex: 1,
    padding: 12,
  },

  // Mobile styles
  mobileContainer: {
    marginLeft: 0,
  },
  mobileContent: {
    padding: 0,
  },

  // Tablet styles
  tabletContainer: {
    marginLeft: 60, // Adjust based on your side bar width
  },
  tabletContent: {
    padding: 10,
  },

  // Desktop styles
  desktopContainer: {
    marginLeft: 240, // Adjust based on your side menu width
  },
  desktopContent: {
    padding: 20,
  },
});

export default StandardPageLayout;
