import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import {
  SeaFilterTags,
  SeaFilterTagsValue,
} from "@src/components/_atoms/SeaFilterTags/SeaFilterTags";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import SeaIcon from "@src/components/_legacy/SeaIcon/SeaIcon";
import { MaintenanceFilters } from "@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters";
import { ViewSparePartDrawer } from "@src/components/_organisms/Maintenance/SparePart/ViewSparePartDrawer";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { renderCategoryName } from "@src/lib/categories";
import {
  formatValue,
  getCurrencyFromRegion,
  truncateText,
} from "@src/lib/util";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { sharedState } from "@src/shared-state/shared-state";
import { SparePart } from "@src/shared-state/VesselMaintenance/spareParts";
import { colors } from "@src/theme/colors";
import React, { useCallback, useMemo, useState } from "react";
import { FlatList, ScrollView, StyleSheet, View } from "react-native";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { usePermission } from "@src/hooks/usePermission";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { EditSparePartDrawer } from "@src/components/_organisms/Maintenance/SparePart/EditSparePartDrawer";

const SparePartsPermissions = {
  maintenanceSchedule: { level: permissionLevels.CREATE },
};

interface SparePartsProps {
  headerSubNavigation: SubNav[];
}

export const SpareParts = ({ headerSubNavigation }: SparePartsProps) => {
  const vessel = sharedState.vessel.use();
  const spareParts = sharedState.spareParts.use();
  const equipment = sharedState.equipment.use();
  const vesselLocations = sharedState.vesselLocations.use();
  const licenseeSettings = sharedState.licenseeSettings.use();
  const vesselSystems = sharedState.vesselSystems.use();

  const router = useRouter();

  const [searchValue, setSearchValue] = useState("");
  const [systemFilter, setSystemFilter] = useState("");
  const [equipmentFilter, setEquipmentFilter] = useState("");
  const [locationFilter, setLocationFilter] = useState("");
  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false);

  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<
    Partial<SeaFilterTagsValue>
  >({
    all: { isActive: true },
    overdue: { isActive: false },
    critical: { isActive: false },
  });

  const modulePermissions = usePermission<typeof SparePartsPermissions>({
    modules: SparePartsPermissions,
  });

  const handleRow = useCallback((item: SparePart) => {
    router.navigate({
      pathname: getRoutePath(Routes.SPARE_PARTS_LIST_VIEW),
      params: {
        vesselId: vessel?.id,
        sparePartId: item.id,
      },
    });
  }, []);

  const checkContainsCritical = useCallback(
    (equipmentIds: string[] | undefined) => {
      if (!equipmentIds) return false;

      return (
        equipmentIds?.some((id) => equipment?.byId[id]?.isCritical) ?? false
      );
    },
    [equipment?.byId],
  );

  const seaTableColumns = useMemo(() => {
    if (!equipment || !licenseeSettings || !vesselLocations) return [];

    return [
      {
        label: "",
        render: (sparePart) => (
          <SeaFileImage files={sparePart.files ?? []} size={"tiny"} />
        ),
      },
      {
        label: "Item",
        value: (sparePart) => `${truncateText(sparePart.item)}`,
        style: { fontWeight: "bold" },
      },
      {
        label: "Quantity",
        value: (sparePart) => {
          return formatValue(sparePart.quantity);
        },
      },
      ...(seaFilterTagsValue.overdue
        ? [
            {
              label: "Order Quantity",
              value: (sparePart: SparePart) => {
                return formatValue(sparePart.orderQuantity);
              },
            },
          ]
        : []),
      {
        label: "Equipment",
        value: (sparePart) => formatValue(sparePart.equipmentList),
      },
      {
        label: "Location",
        value: (sparePart) =>
          formatValue(
            renderCategoryName(sparePart.locationId, vesselLocations),
          ),
      },
      {
        label: "Part #",
        value: (sparePart) => formatValue(sparePart.partNum),
      },
      {
        label: "Unit Price",
        value: (sparePart) =>
          getCurrencyFromRegion(licenseeSettings?.region) +
          formatValue(sparePart.unitPrice),
      },
      {
        label: "Critical",
        value: (sparePart) => checkContainsCritical(sparePart.equipmentIds),
        render: (sparePart) => {
          const isCritical = checkContainsCritical(sparePart.equipmentIds);
          return isCritical ? (
            <SeaIcon icon={"flag"} color={colors.status.critical} />
          ) : (
            <></>
          );
        },
      },
    ] as SeaTableColumn<SparePart>[];
  }, [
    equipment,
    checkContainsCritical,
    licenseeSettings,
    vesselLocations,
    seaFilterTagsValue,
  ]);

  const systemFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return [];

    const options = spareParts.filterOptions.systemIds.map((id: string) => ({
      label: renderCategoryName(id, vesselSystems),
      value: id,
    }));

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [spareParts]);

  const equipmentFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return [];

    const options = spareParts.filterOptions.equipmentIds.map((id: string) => ({
      label: equipment?.byId[id].equipment ?? "",
      value: id,
    }));

    const filteredOptions = options.filter((option) => {
      if (systemFilter) {
        const _equipment = equipment?.byId[option.value];
        return _equipment?.systemId === systemFilter;
      }
      return true;
    });

    return [
      {
        label: "All",
        value: "",
      },
      ...filteredOptions,
    ];
  }, [spareParts, systemFilter]);

  const locationFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return [];
    const options = spareParts.filterOptions.locationIds.map((id: string) => ({
      label: renderCategoryName(id, vesselLocations),
      value: id,
    }));

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [spareParts, vesselLocations]);

  const allData = useMemo(() => {
    const data = spareParts?.all;

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      all: { isActive: prev.all?.isActive ?? false, count: data?.length ?? 0 },
    }));

    return data;
  }, [spareParts]);

  const overDueData = useMemo(() => {
    const data = spareParts?.all.filter(
      (sparePart) =>
        sparePart.orderQuantity && Number(sparePart.orderQuantity) > 0,
    );

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      overdue: {
        isActive: prev.overdue?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }));

    return data;
  }, [spareParts]);

  const criticalData = useMemo(() => {
    const data = spareParts?.all.filter((sparePart) =>
      checkContainsCritical(sparePart.equipmentIds),
    );

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      critical: {
        isActive: prev.critical?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }));

    return data;
  }, [spareParts]);

  // Filter categories]
  const filteredTasks = useMemo(() => {
    let filteredJobs: SparePart[] = [];

    if (seaFilterTagsValue.all?.isActive) {
      filteredJobs = allData ?? [];
    } else {
      if (seaFilterTagsValue.overdue?.isActive) {
        filteredJobs = overDueData ?? [];
      }

      if (seaFilterTagsValue.critical?.isActive) {
        filteredJobs = criticalData ?? [];
      }
    }

    // Filter by system
    if (systemFilter) {
      filteredJobs = filteredJobs.filter(
        (sparePart) => sparePart.systemId === systemFilter,
      );
    }

    // Filter by equipment
    if (equipmentFilter) {
      filteredJobs = filteredJobs.filter((sparePart) =>
        sparePart.equipmentIds?.includes(equipmentFilter),
      );
    }

    //Filter by location
    if (locationFilter) {
      filteredJobs = filteredJobs.filter(
        (sparePart) => sparePart.locationId === locationFilter,
      );
    }

    // Filter by search
    if (searchValue) {
      filteredJobs = filteredJobs.filter((sparePart) => {
        const itemName = sparePart.item?.toLowerCase() ?? "";
        const partNumber = sparePart.partNum?.toLowerCase() ?? "";

        return (
          itemName.includes(searchValue.toLowerCase()) ||
          partNumber.includes(searchValue.toLowerCase())
        );
      });
    }

    return filteredJobs;
  }, [
    allData,
    overDueData,
    criticalData,
    seaFilterTagsValue,
    searchValue,
    systemFilter,
    equipmentFilter,
    locationFilter,
  ]);

  const rows = useMemo(
    () => buildRows(filteredTasks ?? [], (item: SparePart) => handleRow(item)),
    [filteredTasks],
  );

  return (
    <>
      <FlatList
        data={[{ id: "1" }]}
        keyExtractor={(item): string => item.id}
        renderItem={({ __i }) => (
          <RequirePermissions
            role="sparePartsList"
            level={permissionLevels.VIEW}
            showDenial={true}
          >
            <View style={styles.container}>
              <SeaPageCard
                title={
                  vessel?.isShoreFacility
                    ? "Inventory List"
                    : "Spare Parts List"
                }
                primaryActionButton={
                  modulePermissions.maintenanceSchedule ? (
                    <SeaButton
                      onPress={() => setIsVisibleAddDrawer(true)}
                      variant={SeaButtonVariant.Primary}
                      label={"Add New"}
                      iconOptions={{ icon: "add" }}
                    />
                  ) : undefined
                }
                secondaryActionButton={[
                  <SeaDownloadButton
                    key={"download"}
                    onPress={() =>
                      alert("This functionality is not completed yet")
                    }
                  />,
                ]}
                subNav={headerSubNavigation}
              />

              <View
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <ScrollView
                  horizontal
                  style={{
                    width: "100%",
                    display: "flex",
                    flexDirection: "column",
                    paddingVertical: 10,
                  }}
                >
                  <SeaStack
                    direction="row"
                    justify="between"
                    gap={10}
                    style={{ width: "100%" }}
                  >
                    {/* Filter Tags */}
                    <SeaFilterTags
                      value={seaFilterTagsValue}
                      onChange={(value) => setSeaFilterTagsValue(value)}
                    />

                    {/* Filter Row */}
                    <MaintenanceFilters
                      searchFilter={{
                        searchValue: searchValue,
                        setSearchValue: setSearchValue,
                      }}
                      systemFilter={{
                        value: systemFilter,
                        setValue: setSystemFilter,
                        options: systemFilterOptions,
                      }}
                      equipmentFilter={{
                        value: equipmentFilter,
                        setValue: setEquipmentFilter,
                        options: equipmentFilterOptions,
                      }}
                      locationFilter={{
                        value: locationFilter,
                        setValue: setLocationFilter,
                        options: locationFilterOptions,
                      }}
                    />
                  </SeaStack>
                </ScrollView>
              </View>

              {/* Table View */}
              <View style={styles.tableView}>
                <SeaTable
                  columns={seaTableColumns}
                  showGroupedTable={false}
                  rows={rows}
                />
              </View>
            </View>
            {isVisibleAddDrawer && (
              <EditSparePartDrawer
                onClose={() => setIsVisibleAddDrawer(false)}
                visible={isVisibleAddDrawer}
                type="create"
              />
            )}
          </RequirePermissions>
        )}
      />
    </>
  );
};

const buildRows = (items: SparePart[], onPress: (item: SparePart) => void) => {
  return items.map((item) => ({
    data: item,
    // status: getStatus(item),
    onPress: (item: SparePart) => onPress(item),
  }));
};

const styles = StyleSheet.create({
  container: {},
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
});
