import {
  Di<PERSON><PERSON>,
  FlatList,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { sharedState } from "@src/shared-state/shared-state";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { colors } from "@src/theme/colors";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import {
  SeaFilterTags,
  SeaFilterTagsValue,
} from "@src/components/_atoms/SeaFilterTags/SeaFilterTags";
import { ScheduledMaintenanceTask } from "@src/shared-state/VesselMaintenance/maintenanceSchedule";
import React, { useCallback, useMemo, useState } from "react";
import {
  formatDateShort,
  formatInterval,
  formatShortTimeDurationHrsMinsView,
  getDateDifferenceInDays,
  makeDateTime,
  warnDays,
} from "@src/lib/datesAndTime";
import { WhenDueStatus } from "@src/components/_molecules/WhenDueStatus/WhenDueStatus";
import { SeaStatusType } from "@src/types/Common";
import { DateTime } from "luxon";
import { formatValue } from "@src/lib/util";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { MaintenanceFilters } from "@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { useLicenseeSettings } from "@src/hooks/useLicenseeSettings";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { usePermission } from "@src/hooks/usePermission";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { ModifyMaintenanceScheduleDrawer } from "@src/components/_organisms/Maintenance/MaintenanceSchedule/ModifyMaintenanceScheduleDrawer";
import { WhenDueHours } from "@src/components/_molecules/WhenDueHours/WhenDueHours";
import { useDeviceWidth } from "@src/hooks/useDevice";

const { width } = Dimensions.get("window");

const MaintenanceSchedulePermisions = {
  maintenanceSchedule: { level: permissionLevels.CREATE },
};

interface MaintenanceScheduleProps {
  headerSubNavigation?: SubNav[];
}

export const MaintenanceSchedule = ({
  headerSubNavigation,
}: MaintenanceScheduleProps) => {
  const [systemFilter, setSystemFilter] = useState("");
  const [equipmentFilter, setEquipmentFilter] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<
    Partial<SeaFilterTagsValue>
  >({
    all: { isActive: false },
    overdue: { isActive: true },
    upcoming: { isActive: true },
    critical: { isActive: false },
  });

  const router = useRouter();

  const { isTabletWidth } = useDeviceWidth();

  const vessel = sharedState.vessel.use();
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use();
  const vesselSystems = sharedState.vesselSystems.use();
  const equipment = sharedState.equipment.use();

  /**
   * This is intentional as we need to preload the locations to avoid it from randomly disappearing and causing a blank page
   */
  sharedState.vesselLocations.use();

  const [isVisibleAddNewDrawer, setIsVisibleAddNewDrawer] = useState(false);

  const { hasTimeTrackingEnabled } = useLicenseeSettings();
  const modulePermissions = usePermission<typeof MaintenanceSchedulePermisions>(
    {
      modules: MaintenanceSchedulePermisions,
    },
  );

  const updatedTasks = useMemo(() => {
    return scheduledMaintenanceTasks?.prioritised.map((task) => {
      const systemName = renderCategoryName(
        task.equipment?.systemId,
        vesselSystems,
      );

      return {
        ...task,
        systemName,
      };
    });
  }, [scheduledMaintenanceTasks, vesselSystems]);

  const categorizedData = useMemo(() => {
    if (!updatedTasks || !equipment?.byId) return null;

    const allFiltered: ScheduledMaintenanceTask[] = [];
    const overdue: ScheduledMaintenanceTask[] = [];
    const upcoming: ScheduledMaintenanceTask[] = [];
    const critical: ScheduledMaintenanceTask[] = [];

    for (const task of updatedTasks) {
      const eq = equipment.byId[task.equipmentId];
      if (!eq) continue;

      // Apply main filters
      if (systemFilter && eq.systemId !== systemFilter) continue;
      if (equipmentFilter && task.equipmentId !== equipmentFilter) continue;

      allFiltered.push(task);

      const diff = task.dateDue
        ? getDateDifferenceInDays(DateTime.fromISO(task.dateDue))
        : 0;

      if (diff < 0) overdue.push(task);
      if (diff > 0 && diff < warnDays.maintenanceSchedule[0])
        upcoming.push(task);
      if (eq.isCritical) critical.push(task);
    }

    // Update counts once
    setSeaFilterTagsValue((prev) => ({
      ...prev,
      all: { isActive: prev.all?.isActive ?? false, count: allFiltered.length },
      overdue: {
        isActive: prev.overdue?.isActive ?? false,
        count: overdue.length,
      },
      upcoming: {
        isActive: prev.upcoming?.isActive ?? false,
        count: upcoming.length,
      },
      critical: {
        isActive: prev.critical?.isActive ?? false,
        count: critical.length,
      },
    }));

    return {
      all: allFiltered,
      overdue,
      upcoming,
      critical,
    };
  }, [updatedTasks, equipment, systemFilter, equipmentFilter]);

  // Filter categories]
  const filteredTasks = useMemo(() => {
    if (!vesselSystems || !equipment?.byId) return undefined;

    let mergedTasks: ScheduledMaintenanceTask[] = [];

    if (seaFilterTagsValue.all?.isActive) {
      mergedTasks = categorizedData?.all ?? [];
    } else {
      if (seaFilterTagsValue.overdue?.isActive)
        mergedTasks.push(...(categorizedData?.overdue ?? []));
      if (seaFilterTagsValue.upcoming?.isActive)
        mergedTasks.push(...(categorizedData?.upcoming ?? []));
      if (seaFilterTagsValue.critical?.isActive)
        mergedTasks.push(...(categorizedData?.critical ?? []));
    }

    // ✅ Remove duplicates by task.id
    let filteredTasks = Array.from(
      new Map(mergedTasks.map((task) => [task.id, task])).values(),
    );

    // Filter by search
    if (searchValue) {
      filteredTasks = filteredTasks.filter((task) => {
        const taskName = task.task?.toLowerCase() ?? "";
        const equipmentName =
          equipment?.byId[task.equipmentId]?.equipment?.toLowerCase() ?? "";

        let intervalText = "";
        if (
          task.intervalType === "weekMonth" ||
          task.intervalType === "weekMonthAndHours"
        ) {
          intervalText += formatInterval(task.intervalWeekMonth);
        }

        if (task.intervalType === "weekMonthAndHours") {
          intervalText += "\n"; // Equivalent to <br /> in text format
        }

        if (
          task.intervalType === "engineHours" ||
          task.intervalType === "weekMonthAndHours"
        ) {
          intervalText += `${task.intervalEngineHours} Hours`;
        }

        return (
          taskName.includes(searchValue.toLowerCase()) ||
          equipmentName.includes(searchValue.toLowerCase()) ||
          intervalText.toLowerCase().includes(searchValue.toLowerCase())
        );
      });
    }

    return filteredTasks;
  }, [categorizedData, seaFilterTagsValue, searchValue, equipment]);

  const showGroupedChecks = useMemo(
    () => seaFilterTagsValue.all?.isActive,
    [seaFilterTagsValue],
  );

  const filterByEquipmentOptions = useMemo(() => {
    if (!scheduledMaintenanceTasks?.filterOptions) return [];

    const allOptions = scheduledMaintenanceTasks.filterOptions.equipmentIds.map(
      (equipmentId) => {
        const equipmentName = equipment?.byId[equipmentId]?.equipment;
        return {
          label: equipmentName ?? "",
          value: equipmentId,
        };
      },
    );

    const options = allOptions.filter((option) => {
      if (systemFilter) {
        const _equipment = equipment?.byId[option.value];
        return _equipment?.systemId === systemFilter;
      }
      return true;
    });

    return [{ label: "All", value: "" }, ...options];
  }, [scheduledMaintenanceTasks, systemFilter, equipment]);

  const filterBySystemOptions = useMemo(() => {
    if (!scheduledMaintenanceTasks?.filterOptions) return [];

    const options = scheduledMaintenanceTasks.filterOptions.systemIds.map(
      (systemId) => {
        const categoryName = renderCategoryName(systemId, vesselSystems);
        return {
          label: categoryName,
          value: systemId,
        };
      },
    );

    return [{ label: "All", value: "" }, ...options];
  }, [scheduledMaintenanceTasks, vesselSystems]);

  const onPress = useCallback(
    (item: ScheduledMaintenanceTask) => {
      router.navigate({
        pathname: getRoutePath(Routes.MAINTENANCE_SCHEDULE_VIEW),
        params: {
          vesselId: vessel?.id,
          maintenanceScheduleId: item.id,
          name: item.equipment?.equipment,
        },
      });
    },
    [router],
  );

  const rows = useMemo(
    () => buildRows(filteredTasks ?? [], onPress, vesselSystems!),
    [filteredTasks, vesselSystems],
  );

  const columns = useMemo(
    () => buildColumns(hasTimeTrackingEnabled, isTabletWidth),
    [hasTimeTrackingEnabled, isTabletWidth],
  );

  return (
    <>
      <FlatList
        data={[{ id: "1" }]}
        keyExtractor={(item): string => item.id}
        renderItem={({ __i }) => (
          <RequirePermissions
            role="maintenanceSchedule"
            level={permissionLevels.VIEW}
            showDenial={true}
          >
            {/*<ScrollView style={styles.container}>*/}
            <SeaPageCard
              title="Maintenance Schedule"
              primaryActionButton={
                modulePermissions.maintenanceSchedule ? (
                  <SeaButton
                    onPress={() => setIsVisibleAddNewDrawer(true)}
                    variant={SeaButtonVariant.Primary}
                    label={"Add New"}
                    iconOptions={{ icon: "add" }}
                  />
                ) : undefined
              }
              secondaryActionButton={[
                <SeaDownloadButton
                  key={"download"}
                  onPress={() =>
                    alert("This functionality is not completed yet")
                  }
                />,
                <SeaSettingsButton
                  key={"settings"}
                  onPress={() =>
                    alert("This functionality is not completed yet")
                  }
                />,
              ]}
              subNav={headerSubNavigation}
            />

            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <SeaStack
                direction="row"
                justify="between"
                align="center"
                gap={10}
                style={{ width: "100%" }}
              >
                {/* Filter Tags */}
                <SeaFilterTags
                  value={seaFilterTagsValue}
                  onChange={(value) => setSeaFilterTagsValue(value)}
                />

                {/* Filter Row */}
                <MaintenanceFilters
                  searchFilter={{
                    searchValue,
                    setSearchValue,
                  }}
                  systemFilter={{
                    value: systemFilter,
                    setValue: (value) => {
                      setSystemFilter(value);
                      setEquipmentFilter("");
                    },
                    options: filterBySystemOptions,
                  }}
                  equipmentFilter={{
                    value: equipmentFilter,
                    setValue: setEquipmentFilter,
                    options: filterByEquipmentOptions,
                  }}
                  setSeaFilterTagsValue={() => {
                    setSeaFilterTagsValue({
                      all: { isActive: false },
                      overdue: { isActive: true },
                      upcoming: { isActive: true },
                      critical: { isActive: false },
                    });
                  }}
                />
              </SeaStack>
            </ScrollView>

            {/* Table View */}
            <View style={styles.tableView}>
              {/*<ScrollView>*/}
              <SeaTable
                columns={columns}
                showGroupedTable={showGroupedChecks}
                rows={rows}
                sortFunction={(a, b) => {
                  if (!a.equipment || !b.equipment) return 0;
                  return a.equipment?.equipment?.localeCompare(
                    b.equipment?.equipment || "",
                  );
                }}
              />
              {/*</ScrollView>*/}
            </View>
            {/*</ScrollView>*/}
            {isVisibleAddNewDrawer && (
              <ModifyMaintenanceScheduleDrawer
                visible={isVisibleAddNewDrawer}
                onClose={() => setIsVisibleAddNewDrawer(false)}
                mode="create"
              />
            )}
          </RequirePermissions>
        )}
      />
    </>
  );
};

const buildColumns = (hasTimeTrackingEnabled: boolean, isTablet?: boolean) => {
  return [
    {
      label: "Equipment",
      value: (x) =>
        `${x?.equipment?.equipment} ${x.equipment?.state === "deleted" ? " (deleted)" : ""}`,
      style: { fontWeight: "bold" },
      widthPercentage: 0.3,
    },
    {
      label: "Maintenance Task",
      value: (x) => {
        return x.task;
      },
      widthPercentage: 0.2,
    },
    ...(!isTablet
      ? [
          {
            label: "Tags",
            value: (x: ScheduledMaintenanceTask) =>
              x.maintenanceTags?.join(", "),
            isHidden: width < 550,
            compactModeOptions: {
              hideRow: true,
            },
          },
        ]
      : []),
    {
      label: "Interval",
      value: (x) => {
        let intervalText = "";

        if (
          x.intervalType === "weekMonth" ||
          x.intervalType === "weekMonthAndHours"
        ) {
          intervalText += formatInterval(x.intervalWeekMonth);
        }

        if (x.intervalType === "weekMonthAndHours") {
          intervalText += "\n"; // Equivalent to <br /> in text format
        }

        if (
          (x.intervalType === "engineHours" ||
            x.intervalType === "weekMonthAndHours") &&
          x.intervalEngineHours
        ) {
          intervalText += `${x.intervalEngineHours} Hours`;
        }

        return intervalText;
      },
    },
    {
      label: "Next Due",
      value: (x) => {
        let nextDueText = "";

        if (
          x.intervalType === "weekMonth" ||
          x.intervalType === "weekMonthAndHours"
        ) {
          nextDueText += formatDateShort(x.dateDue);
        }

        if (x.intervalType === "weekMonthAndHours") {
          nextDueText += "\n"; // Equivalent to <br /> in text format
        }

        if (
          x.intervalType === "engineHours" ||
          x.intervalType === "weekMonthAndHours"
        ) {
          nextDueText += `${x.engineHoursDue} Hours`;
        }

        return nextDueText;
      },
    },
    ...(hasTimeTrackingEnabled
      ? [
          {
            label: "Estimated time",
            value: (x: ScheduledMaintenanceTask) =>
              formatValue(
                x.estimatedTime
                  ? formatShortTimeDurationHrsMinsView(x.estimatedTime)
                  : "-",
              ),
            compactModeOptions: {
              hideRow: true,
            },
          },
        ]
      : []),
    {
      label: "Status",
      render: (x) =>
        x.useHours && x.engineHoursLeft ? (
          <WhenDueHours engineHoursLeft={x.engineHoursLeft} />
        ) : x.dateDue ? (
          <WhenDueStatus
            whenDue={x.dateDue}
            warnDaysThreshold={warnDays.maintenanceSchedule[0]}
            hasFault={x.hasFault ?? false}
          />
        ) : (
          <></>
        ),
      width: 150,
    },
    {
      label: "Critical",
      value: (x) => x.equipment?.isCritical,
      render: (x) => (
        <>
          {x.equipment?.isCritical ? (
            <SeaIcon icon={"flag"} color={colors.status.critical} />
          ) : null}
        </>
      ),
      width: 100,
    },
  ] as SeaTableColumn<ScheduledMaintenanceTask>[];
};

const buildRows = (
  items: ScheduledMaintenanceTask[],
  onPress: (item: ScheduledMaintenanceTask) => void,
  maintenanceCategory: CategoriesData,
) => {
  return items.map((item) => ({
    data: item,
    status: getStatus(item),
    onPress: (item: ScheduledMaintenanceTask) => onPress(item),
    group: (item: ScheduledMaintenanceTask) =>
      renderCategoryName(item.equipment?.systemId, maintenanceCategory),
  }));
};

const getStatus = (item: ScheduledMaintenanceTask) => {
  if (item.useHours && item.engineHoursLeft) {
    if (item.engineHoursLeft < 0) {
      return SeaStatusType.Error;
    } else if (item.engineHoursLeft < 50) {
      return SeaStatusType.Warning;
    }
  } else {
    const due = makeDateTime(item.dateDue);
    const days = due.diff(makeDateTime(), ["days"]).days;

    if (days < 0) {
      return SeaStatusType.Error;
    } else if (days < warnDays.maintenanceSchedule[0]) {
      return SeaStatusType.Warning;
    }
  }

  return SeaStatusType.Ok;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
});
